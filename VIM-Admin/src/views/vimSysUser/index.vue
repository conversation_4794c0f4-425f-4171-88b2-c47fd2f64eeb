<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <!--     按照用户ID搜索 -->
      <el-form-item label="用户ID" prop="id">
        <el-input
            v-model="queryParams.id"
            placeholder="请输入用户ID"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="用户昵称" prop="nickname">
        <el-input
            v-model="queryParams.nickname"
            placeholder="请输入用户昵称"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号码"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份" prop="identity">
        <el-select
            v-model="queryParams.identity"
            placeholder="请选择身份"
            clearable
            style="width: 120px"
        >
          <el-option
              v-for="item in identityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select
            v-model="queryParams.state"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
        >
          <el-option label="正常" value="1"/>
          <el-option label="禁用" value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item label="注册IP" prop="createIp">
        <el-input
            v-model="queryParams.createIp"
            placeholder="请输入注册IP地址"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['VimUserSys:VimUsers:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['VimUserSys:VimUsers:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['VimUserSys:VimUsers:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['VimUserSys:VimUsers:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="vimUsersList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="用户ID" align="center" prop="id" width="80"/>
      <el-table-column label="用户名" align="center" prop="username" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-button
              link
              type="primary"
              @click="handleViewUserOrders(scope.row)"
              v-hasPermi="['VimUserSys:VimUsers:query']"
          >
            {{ scope.row.username }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="昵称" align="center" prop="nickname" :show-overflow-tooltip="true"/>
      <el-table-column label="最后登录IP" align="center" prop="lastLoginIp" width="140" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.lastLoginIp">
            <div class="ip-address">{{ scope.row.lastLoginIp }}</div>
            <div v-if="ipLocations[scope.row.lastLoginIp]" class="ip-location">
              {{ ipLocations[scope.row.lastLoginIp].location }}
            </div>
          </div>
          <span v-else class="text-muted">未知</span>
        </template>
      </el-table-column>
      <el-table-column label="注册IP" align="center" prop="createIp" width="140" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.createIp">
            <div class="ip-address">{{ scope.row.createIp }}</div>
            <div v-if="ipLocations[scope.row.createIp]" class="ip-location">
              {{ ipLocations[scope.row.createIp].location }}
            </div>
          </div>
          <span v-else class="text-muted">未知</span>
        </template>
      </el-table-column>
      <!--      <el-table-column hidden="hidden" label="手机号码" align="center" prop="phone" width="120" />-->
      <el-table-column label="状态" align="center" prop="state" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.state === 1" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.state === 2" type="danger">禁用</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="身份" align="center" prop="identity" width="140">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
            <el-tag v-if="scope.row.identity == 1" type="warning">普通用户</el-tag>
            <el-tag v-if="scope.row.identity == 2" type="success">线上主播</el-tag>
            <el-tag v-if="scope.row.identity == 3" type="primary">线下主播</el-tag>
            <el-tag v-if="scope.row.identity == 4" type="info">代理</el-tag>
            <el-button
                link
                type="primary"
                icon="Edit"
                size="small"
                @click="handleUpdateIdentity(scope.row)"
                v-hasPermi="['VimUserSys:VimUsers:edit']"
                title="修改身份"
            ></el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="电能" align="center" prop="coin" width="100">
        <template #default="scope">
          <el-tag type="warning">{{ scope.row.coin || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="钥匙数量" align="center" prop="key" width="100">
        <template #default="scope">
          <el-tag type="warning">{{ scope.row.key || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="邀请码" align="center" prop="inviteCode" width="100">
        <template #default="scope">
          <el-button
              link
              type="primary"
              @click="handleViewSubordinates(scope.row)"
              v-hasPermi="['VimUserSys:VimUsers:query']"
              :disabled="!scope.row.inviteCode"
          >
            {{ scope.row.inviteCode || '-' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="vimUsercreateTime" width="160" sortable>
        <template #default="scope">
          <span>{{ formatCreateTime(scope.row.vimUsercreateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后登录时间" align="center" prop="lastLoginTime" width="160">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.lastLoginTime) }}</span>
        </template>
      </el-table-column>
      <!-- 新增用户首充时间列 -->
      <el-table-column label="用户首充时间" align="center" prop="firstRechargeTimeStr" width="160" sortable>
        <template #default="scope">
          <span v-if="scope.row.firstRechargeTimeStr" class="first-recharge-time">
            {{ scope.row.firstRechargeTimeStr }}
          </span>
          <span v-else class="no-recharge">暂无充值</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <!-- 主要操作按钮 -->
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['VimUserSys:VimUsers:edit']"
            size="small"
          >
            修改
          </el-button>

          <el-button
            link
            :type="scope.row.state === 1 ? 'danger' : 'success'"
            :icon="scope.row.state === 1 ? 'Lock' : 'Unlock'"
            @click="handleToggleUserState(scope.row)"
            v-hasPermi="['VimUserSys:VimUsers:edit']"
            size="small"
          >
            {{ scope.row.state === 1 ? '禁用' : '启用' }}
          </el-button>

          <!-- 更多操作下拉菜单 -->
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, scope.row)"
            trigger="click"
            size="small"
          >
            <el-button link type="primary" size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  command="viewBag"
                  icon="Goods"
                  v-if="checkPermission(['VimUserSys:VimUsers:query'])"
                >
                  查看背包
                </el-dropdown-item>
                <el-dropdown-item
                  command="resetPassword"
                  icon="Key"
                  v-if="checkPermission(['VimUserSys:VimUsers:edit'])"
                >
                  重置密码
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  icon="Delete"
                  v-if="checkPermission(['VimUserSys:VimUsers:remove'])"
                  divided
                >
                  删除用户
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handlePagination"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="vimUsersRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名"/>
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称"/>
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码"/>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!form.id">
          <el-input v-model="form.password" placeholder="请输入密码" show-password/>
        </el-form-item>
        <el-form-item label="邀请码" prop="inviteCode">
          <el-input v-model="form.inviteCode" placeholder="请输入邀请码"/>
        </el-form-item>
        <el-form-item label="电能" prop="coin">
          <el-input v-model="form.coin" placeholder="请输入电能数量"/>
        </el-form-item>
        <el-form-item label="钥匙数量" prop="key">
          <el-input v-model="form.key" placeholder="请输入钥匙数量"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户背包弹层 -->
    <el-dialog
        title="用户信息详情"
        v-model="bagDialogVisible"
        width="950px"
        append-to-body
        destroy-on-close
    >
      <div class="bag-dialog-content">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="user-detail-tabs">
          <!-- 背包标签页 -->
          <el-tab-pane label="用户背包" name="bag">
            <div v-loading="bagLoading">
              <!-- 搜索和排序工具栏 -->
              <div class="bag-toolbar">
                <el-input
                    v-model="bagQueryParams.itemName"
                    placeholder="搜索物品名称"
                    clearable
                    @keyup.enter="handleBagSearch"
                    class="search-input"
                >
                  <template #append>
                    <el-button @click="handleBagSearch">
                      <el-icon>
                        <Search/>
                      </el-icon>
                    </el-button>
                  </template>
                </el-input>

                <el-select v-model="bagQueryParams.sortField" placeholder="排序方式" @change="handleBagSort"
                           class="sort-select">
                  <el-option label="价格从高到低" value="priceDesc"/>
                  <el-option label="价格从低到高" value="priceAsc"/>
                  <el-option label="最新获得" value="timeDesc"/>
                </el-select>
              </div>

        <div v-if="bagItems.length > 0" class="bag-content">
          <div class="bag-header">
            <div class="user-info">
              <span class="user-name">{{ currentUser.username }}</span>
              <span class="user-level">等级: {{ currentUser.level || 0 }}</span>
              <span class="user-coin">电能: {{ currentUser.coin || 0 }}</span>
            </div>
            <div class="bag-count">
              <span>背包物品: {{ total }} 件</span>
            </div>
          </div>

          <el-row :gutter="12" class="bag-container">
            <el-col :xs="8" :sm="6" :md="4" v-for="item in bagItems" :key="item.id">
              <el-card shadow="hover" class="bag-item-card">
                <div class="bag-item-image">
                  <el-image
                      :src="item.image"
                      fit="contain"
                      :preview-src-list="[item.image]"
                      :initial-index="0"
                      class="item-image"
                      alt="物品图片"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon>
                          <Picture/>
                        </el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div class="bag-item-info">
                  <el-tooltip :content="item.name || '未知物品'" placement="top" :show-after="500">
                    <div class="item-name">{{ item.name || '未知物品' }}</div>
                  </el-tooltip>
                  <div class="item-rarity" :class="getTagClass(item.tag)">
                    {{ item.tag || '普通' }}
                  </div>
                  <div class="item-price">¥{{ item.priceShow || 0 }}</div>
                  <div class="item-count">x{{ item.count || 1 }}</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 分页控件 -->
          <div class="pagination-container">
            <el-pagination
                v-model:currentPage="bagQueryParams.pageNum"
                v-model:page-size="bagQueryParams.pageSize"
                :page-sizes="[12, 24, 36, 48]"
                :small="false"
                :disabled="false"
                :background="true"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleBagSizeChange"
                @current-change="handleBagCurrentChange"
            />
          </div>
        </div>
        <el-empty v-else description="背包暂无物品"/>
            </div>
          </el-tab-pane>

          <!-- 上级推广者标签页 -->
          <el-tab-pane label="上级推广者" name="superior">
            <div v-loading="superiorLoading">
              <div v-if="superiorUser" class="superior-info">
                <el-card shadow="hover" class="superior-card">
                  <template #header>
                    <div class="card-header">
                      <span>上级推广者信息</span>
                    </div>
                  </template>
                  <div class="superior-content">
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <div class="info-item">
                          <label>用户ID：</label>
                          <span>{{ superiorUser.id }}</span>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="info-item">
                          <label>昵称：</label>
                          <span>{{ superiorUser.nickname || '未设置' }}</span>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <div class="info-item">
                          <label>手机号：</label>
                          <span>{{ superiorUser.phone || '未设置' }}</span>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="info-item">
                          <label>用户身份：</label>
                          <el-tag
                            :type="getIdentityTagType(superiorUser.identity)"
                            size="small"
                          >
                            {{ superiorUser.identityName || '未知' }}
                          </el-tag>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="24">
                        <div class="info-item">
                          <label>注册时间：</label>
                          <span>{{ superiorUser.createTimeStr || '未知' }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-card>
              </div>
              <el-empty v-else description="该用户没有上级推广者" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 修改用户身份对话框 -->
    <el-dialog title="修改用户身份" v-model="identityDialogVisible" width="500px" append-to-body>
      <el-form ref="identityFormRef" :model="identityForm" :rules="identityRules" label-width="100px">
        <el-form-item label="用户信息">
          <div style="color: #606266;">
            <p><strong>用户名：</strong>{{ identityForm.username }}</p>
            <p><strong>昵称：</strong>{{ identityForm.nickname }}</p>
            <p><strong>手机号：</strong>{{ identityForm.phone }}</p>
            <p><strong>当前身份：</strong>
              <el-tag v-if="identityForm.currentIdentity == 1" type="warning">普通用户</el-tag>
              <el-tag v-if="identityForm.currentIdentity == 2" type="success">线上主播</el-tag>
              <el-tag v-if="identityForm.currentIdentity == 3" type="primary">线下主播</el-tag>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="新身份" prop="identity">
          <el-radio-group v-model="identityForm.identity">
            <el-radio :label="1">普通用户</el-radio>
            <el-radio :label="2">线上主播</el-radio>
            <el-radio :label="3">线下主播</el-radio>
            <el-radio :label="4">代理</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
              v-model="identityForm.remark"
              type="textarea"
              placeholder="请输入修改身份的原因或备注"
              :rows="3"
              maxlength="200"
              show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelIdentityForm">取 消</el-button>
          <el-button type="primary" @click="submitIdentityForm" :loading="identitySubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 下级玩家列表对话框 -->
    <el-dialog title="下级玩家列表" v-model="subordinatesDialogVisible" width="1200px" append-to-body>
      <div style="margin-bottom: 16px; color: #606266;">
        <p><strong>邀请人：</strong>{{ currentInviter.username }} ({{ currentInviter.nickname }})</p>
        <p><strong>邀请码：</strong>{{ currentInviter.inviteCode }}</p>
      </div>

      <el-table v-loading="subordinatesLoading" :data="subordinatesList" style="width: 100%">
        <el-table-column label="用户ID" align="center" prop="id" width="80"/>
        <el-table-column label="用户名" align="center" prop="username" :show-overflow-tooltip="true"/>
        <el-table-column label="昵称" align="center" prop="nickname" :show-overflow-tooltip="true"/>
        <el-table-column label="手机号码" align="center" prop="phone" width="120">
          <template #default="scope">
            <span>{{ scope.row.phone ? scope.row.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="身份" align="center" prop="identity" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.identity == 1" type="warning">普通用户</el-tag>
            <el-tag v-if="scope.row.identity == 2" type="success">线上主播</el-tag>
            <el-tag v-if="scope.row.identity == 3" type="primary">线下主播</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="电能" align="center" prop="coin" width="100">
          <template #default="scope">
            <el-tag type="warning">{{ scope.row.coin || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="钥匙数量" align="center" prop="key" width="100">
          <template #default="scope">
            <el-tag type="warning">{{ scope.row.key || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="累计充值" align="center" prop="totalRecharge" width="120">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.totalRecharge > 0">
              ¥{{ formatCurrency(scope.row.totalRecharge) }}
            </el-tag>
            <el-tag type="info" v-else>¥0.00</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最后登录时间" align="center" prop="lastLoginTime" width="160">
          <template #default="scope">
            <span>{{ formatDateTime(scope.row.lastLoginTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
          v-show="subordinatesTotal > 0"
          :total="subordinatesTotal"
          v-model:page="subordinatesQueryParams.pageNum"
          v-model:limit="subordinatesQueryParams.pageSize"
          @pagination="handleSubordinatesPagination"
          style="margin-top: 16px;"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="subordinatesDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户订单明细对话框 -->
    <el-dialog
        title="用户订单明细"
        v-model="userOrdersDialogVisible"
        width="1200px"
        :close-on-click-modal="false"
    >
      <!-- 用户信息 -->
      <div style="margin-bottom: 20px; padding: 16px; background-color: #f5f7fa; border-radius: 4px;">
        <el-row :gutter="20">
          <el-col :span="6">
            <strong>用户ID：</strong>{{ currentOrderUser.id }}
          </el-col>
          <el-col :span="6">
            <strong>用户名：</strong>{{ currentOrderUser.username }}
          </el-col>
          <el-col :span="6">
            <strong>昵称：</strong>{{ currentOrderUser.nickname }}
          </el-col>
          <el-col :span="6">
            <strong>手机号：</strong>{{ currentOrderUser.phone }}
          </el-col>
        </el-row>
      </div>

      <!-- 订单类型切换 -->
      <el-tabs v-model="activeOrderTab" @tab-click="handleOrderTabClick">
        <!-- 消费订单明细 -->
        <el-tab-pane label="消费订单明细" name="box">
          <el-table
              v-loading="boxOrdersLoading"
              :data="boxOrdersList"
              style="width: 100%"
              max-height="400"
          >
            <el-table-column label="订单ID" align="center" prop="id" width="180" :show-overflow-tooltip="true"/>
            <el-table-column label="消费金额" align="center" prop="amount" width="120">
              <template #default="scope">
                <el-tag type="warning">¥{{ scope.row.amount || 0 }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="余额" align="center" prop="balance" width="120">
              <template #default="scope">
                <el-tag type="info">{{ scope.row.balance !== null ? '¥' + scope.row.balance : '-' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true"/>
            <el-table-column label="创建时间" align="center" prop="createTime" width="160">
              <template #default="scope">
                <span>{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <pagination
              v-show="boxOrdersTotal > 0"
              :total="boxOrdersTotal"
              v-model:page="boxOrdersQueryParams.pageNum"
              v-model:limit="boxOrdersQueryParams.pageSize"
              @pagination="handleBoxOrdersPagination"
              style="margin-top: 16px;"
          />
        </el-tab-pane>

        <!-- 充值订单明细 -->
        <el-tab-pane label="充值订单明细" name="recharge">
          <el-table
              v-loading="rechargeOrdersLoading"
              :data="rechargeOrdersList"
              style="width: 100%"
              max-height="400"
          >
            <el-table-column label="订单ID" align="center" prop="id" width="100"/>
            <el-table-column label="充值金额" align="center" prop="amount" width="120">
              <template #default="scope">
                <el-tag type="success">¥{{ scope.row.amount || 0 }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="获得电能" align="center" prop="coin" width="100">
              <template #default="scope">
                <el-tag type="warning">{{ scope.row.coin || 0 }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="支付方式" align="center" prop="payType" width="100">
              <template #default="scope">
                <el-tag v-if="scope.row.payType == 1" type="primary">微信</el-tag>
                <el-tag v-else-if="scope.row.payType == 2" type="success">支付宝</el-tag>
                <el-tag v-else>其他</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="订单状态" align="center" prop="status" width="100">
              <template #default="scope">
                <el-tag v-if="scope.row.status == 1" type="success">已完成</el-tag>
                <el-tag v-else-if="scope.row.status == 0" type="warning">待支付</el-tag>
                <el-tag v-else type="danger">已取消</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="支付时间" align="center" prop="payTime" width="160">
              <template #default="scope">
                <span>{{ formatDateTime(scope.row.payTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="160">
              <template #default="scope">
                <span>{{ formatDateTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <pagination
              v-show="rechargeOrdersTotal > 0"
              :total="rechargeOrdersTotal"
              v-model:page="rechargeOrdersQueryParams.pageNum"
              v-model:limit="rechargeOrdersQueryParams.pageSize"
              @pagination="handleRechargeOrdersPagination"
              style="margin-top: 16px;"
          />
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userOrdersDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.ip-address {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #409EFF;
  font-weight: bold;
}

.ip-location {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
  line-height: 1.2;
}

.text-muted {
  color: #C0C4CC;
  font-style: italic;
}

/* 上级推广者信息样式 */
.superior-info {
  padding: 20px 0;
}

.superior-card {
  max-width: 600px;
  margin: 0 auto;
}

.superior-content {
  padding: 10px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  margin-right: 12px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.user-detail-tabs {
  margin-top: 10px;
}

.user-detail-tabs .el-tabs__content {
  padding-top: 20px;
}
</style>

<script setup name="VimUsers">
import {
  addVimUsers,
  delVimUsers,
  getSubordinateUsers,
  getUserBag,
  getUserInfo,
  getUserSuperior,
  listVimUsers,
  resetVimUserPassword,
  updateUserIdentity,
  updateUserState,
  updateVimUsers
} from "@/api/VimUserSys/VimUsers";
import {getUserConsumeList, getUserRechargeList} from "@/api/promotion/anchor";
import {useRouter} from 'vue-router';
import {batchIpToLocation} from '@/utils/ipUtils';
import {checkPermi} from '@/utils/permission';
import {ArrowDown, Loading, Lock, Unlock} from '@element-plus/icons-vue';

const router = useRouter();
const {proxy} = getCurrentInstance();

// IP地址到地理位置的映射
const ipLocations = ref({});

const identityOptions = ref([
  {value: '1', label: '普通用户'},
  {value: '2', label: '线上主播'},
  {value: '3', label: '线下主播'},
  {value: '4', label: '代理'}
]);

const vimUsersList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    nickname: undefined,
    phone: undefined,
    identity: undefined,
    state: undefined,
    createIp: undefined,
  },
  rules: {
    username: [
      {required: true, message: "用户名不能为空", trigger: "blur"}
    ],
    nickname: [
      {required: true, message: "昵称不能为空", trigger: "blur"}
    ],
    phone: [
      {required: true, message: "手机号码不能为空", trigger: "blur"},
      {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur"}
    ],
    password: [
      {required: true, message: "密码不能为空", trigger: "blur"},
      {min: 6, max: 20, message: "密码长度必须介于 6 和 20 之间", trigger: "blur"}
    ],
    level: [
      {required: true, message: "等级不能为空", trigger: "blur"},
      {pattern: /^\d+$/, message: "只能输入数字", trigger: ["blur", "change"]}
    ],
    coin: [
      {required: true, message: "电能不能为空", trigger: "blur"},
      {pattern: /^\d+$/, message: "只能输入数字", trigger: ["blur", "change"]}
    ]
  }
});

const queryParams = toRef(data, 'queryParams');
const form = toRef(data, 'form');
const rules = toRef(data, 'rules');

// 确保分页参数为数字类型
watchEffect(() => {
  if (queryParams.value) {
    queryParams.value.pageNum = Number(queryParams.value.pageNum);
    queryParams.value.pageSize = Number(queryParams.value.pageSize);
  }
});

/** 查询盲盒用户列表 */
function getList() {
  loading.value = true;
  listVimUsers(queryParams.value).then(response => {
    vimUsersList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 提取所有IP地址（包括最后登录IP和注册IP）
    const ipList = [];
    response.rows.forEach(user => {
      if (user.lastLoginIp && user.lastLoginIp.trim() !== '') {
        ipList.push(user.lastLoginIp);
      }
      if (user.createIp && user.createIp.trim() !== '') {
        ipList.push(user.createIp);
      }
    });
    // 去重
    const uniqueIpList = [...new Set(ipList)];

    // 批量转换IP地址为地理位置
    if (uniqueIpList.length > 0) {
      batchIpToLocation(uniqueIpList).then(locationMap => {
        // 将结果转换为普通对象
        const locations = {};
        locationMap.forEach((value, key) => {
          locations[key] = value;
        });
        ipLocations.value = locations;
      });
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: undefined,
    username: undefined,
    nickname: undefined,
    phone: undefined,
    password: undefined,
    inviteCode: undefined,
    level: 0,
    coin: 0,
    remark: undefined,
  };
  proxy.resetForm("vimUsersRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加盲盒用户";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getUserInfo(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改盲盒用户";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["vimUsersRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateVimUsers(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVimUsers(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除盲盒用户编号为"' + ids + '"的数据项？').then(function () {
    return delVimUsers(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 重置密码按钮操作 */
function handleResetPassword(row) {
  const userId = row.id;
  const username = row.username || '该用户';

  proxy.$modal.confirm(`确认要重置用户"${username}"的密码吗？重置后密码将变为默认密码"123456"。`, "重置密码确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function () {
    return resetVimUserPassword(userId);
  }).then(() => {
    proxy.$modal.msgSuccess("密码重置成功，新密码为：123456");
  }).catch((error) => {
    if (error !== 'cancel') {
      proxy.$modal.msgError("密码重置失败");
    }
  });
}

/** 切换用户状态（禁用/启用）*/
function handleToggleUserState(row) {
  const userId = row.id;
  const username = row.username || row.nickname || '该用户';
  const currentState = row.state;
  const newState = currentState === 1 ? 2 : 1;
  const actionText = currentState === 1 ? '禁用' : '启用';
  const confirmText = currentState === 1 ?
    `确认要禁用用户"${username}"吗？禁用后该用户将无法正常使用系统功能。` :
    `确认要启用用户"${username}"吗？启用后该用户可以正常使用系统功能。`;

  proxy.$modal.confirm(confirmText, `${actionText}用户确认`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function () {
    return updateUserState(userId, newState);
  }).then(() => {
    proxy.$modal.msgSuccess(`用户${actionText}成功`);
    getList(); // 刷新用户列表
  }).catch((error) => {
    if (error !== 'cancel') {
      proxy.$modal.msgError(`用户${actionText}失败`);
    }
  });
}

/** 下拉菜单命令处理 */
function handleDropdownCommand(command, row) {
  switch (command) {
    case 'viewBag':
      handleViewBag(row);
      break;
    case 'resetPassword':
      handleResetPassword(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.warn('未知的下拉菜单命令:', command);
  }
}

/** 权限检查方法 */
function checkPermission(permissions) {
  if (!permissions || !Array.isArray(permissions)) {
    return false;
  }
  return checkPermi(permissions);
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('VimUserSys/VimUsers/export', {
    ...queryParams.value
  }, `vimUsers_${new Date().getTime()}.xlsx`);
}

// 背包相关变量
const bagDialogVisible = ref(false);
const bagLoading = ref(false);
const bagItems = ref([]);
const currentUser = ref({});
const bagQueryParams = ref({
  pageNum: 1,
  pageSize: 12,
  userId: undefined,
  itemName: '',
  sortField: 'priceDesc'
});

// 标签页和上级推广者相关变量
const activeTab = ref('bag');
const superiorLoading = ref(false);
const superiorUser = ref(null);

// 物品稀有度标签
const rarityLabels = {
  1: '崭新出厂',
  2: '略有磨损',
  3: '久经沙场',
  4: '破损不堪',
  5: '战痕累累'
};

// 修改身份相关变量
const identityDialogVisible = ref(false);
const identitySubmitting = ref(false);
const identityForm = ref({
  userId: undefined,
  username: '',
  nickname: '',
  phone: '',
  currentIdentity: undefined,
  identity: undefined,
  remark: ''
});

const identityRules = ref({
  identity: [
    {required: true, message: "请选择新身份", trigger: "change"}
  ],
  remark: [
    {required: true, message: "请输入修改原因或备注", trigger: "blur"},
    {min: 5, max: 200, message: "备注长度必须介于 5 和 200 之间", trigger: "blur"}
  ]
});

// 下级玩家列表相关变量
const subordinatesDialogVisible = ref(false);
const subordinatesLoading = ref(false);
const subordinatesList = ref([]);
const subordinatesTotal = ref(0);
const currentInviter = ref({});
const subordinatesQueryParams = ref({
  pageNum: 1,
  pageSize: 10
});

// 用户订单明细相关变量
const userOrdersDialogVisible = ref(false);
const currentOrderUser = ref({});
const activeOrderTab = ref('box');

// 消费订单相关变量
const boxOrdersLoading = ref(false);
const boxOrdersList = ref([]);
const boxOrdersTotal = ref(0);
const boxOrdersQueryParams = ref({
  pageNum: 1,
  pageSize: 10
});

// 充值订单相关变量
const rechargeOrdersLoading = ref(false);
const rechargeOrdersList = ref([]);
const rechargeOrdersTotal = ref(0);
const rechargeOrdersQueryParams = ref({
  pageNum: 1,
  pageSize: 10
});

/** 查看背包按钮操作 */
function handleViewBag(row) {
  currentUser.value = row;
  bagDialogVisible.value = true;
  activeTab.value = 'bag'; // 默认显示背包标签页
  bagLoading.value = true;
  bagQueryParams.value.userId = row.id;

  // 获取背包数据
  getUserBag(bagQueryParams.value).then(response => {
    bagItems.value = response.rows || [];
    total.value = response.total || 0;
    bagLoading.value = false;
  }).catch(() => {
    bagLoading.value = false;
    proxy.$modal.msgError("获取背包数据失败");
  });

  // 获取上级推广者信息
  loadSuperiorUser(row.id);
}

/** 获取上级推广者信息 */
function loadSuperiorUser(userId) {
  superiorLoading.value = true;
  superiorUser.value = null;

  getUserSuperior(userId).then(response => {
    superiorUser.value = response.data;
    superiorLoading.value = false;
  }).catch(() => {
    superiorUser.value = null;
    superiorLoading.value = false;
    // 不显示错误信息，因为没有上级推广者是正常情况
  });
}

/** 获取身份标签类型 */
function getIdentityTagType(identity) {
  switch (identity) {
    case 1:
      return 'warning'; // 普通用户
    case 2:
      return 'success'; // 线上主播
    case 3:
      return 'primary'; // 线下主播
    default:
      return '代理';
  }
}

// 时间格式化函数
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
};

// 格式化创建时间（注册时间）
const formatCreateTime = (timestamp) => {
  if (!timestamp) return '-';
  // 如果是秒级时间戳，转换为毫秒级
  const time = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
  const date = new Date(time);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
};

// 格式化货币金额
const formatCurrency = (amount) => {
  if (!amount || amount === 0) return '0.00';
  return Number(amount).toFixed(2);
};

/** 分页事件处理 */
function handlePagination({page, limit}) {
  queryParams.value.pageNum = Number(page);
  queryParams.value.pageSize = Number(limit);
  getList();
}

/** 获取标签类名 */
function getTagClass(tag) {
  if (!tag) return '';
  return 'rarity-' + tag;
}

/** 处理背包分页大小变化 */
function handleBagSizeChange(newSize) {
  bagQueryParams.value.pageSize = newSize;
  handleBagCurrentChange(1);
}

/** 处理背包当前页变化 */
function handleBagCurrentChange(newPage) {
  bagQueryParams.value.pageNum = newPage;
  handleViewBag(currentUser.value);
}

/** 处理背包搜索 */
function handleBagSearch() {
  bagQueryParams.value.pageNum = 1;
  handleViewBag(currentUser.value);
}

/** 处理背包排序 */
function handleBagSort() {
  bagQueryParams.value.pageNum = 1;
  handleViewBag(currentUser.value);
}

/** 修改用户身份按钮操作 */
function handleUpdateIdentity(row) {
  identityForm.value = {
    userId: row.id,
    username: row.username,
    nickname: row.nickname,
    phone: row.phone,
    currentIdentity: row.identity,
    identity: undefined,
    remark: ''
  };
  identityDialogVisible.value = true;
}

/** 提交身份修改 */
function submitIdentityForm() {
  proxy.$refs["identityFormRef"].validate(valid => {
    if (valid) {
      if (identityForm.value.identity === identityForm.value.currentIdentity) {
        proxy.$modal.msgWarning("新身份与当前身份相同，无需修改");
        return;
      }

      identitySubmitting.value = true;
      const data = {
        identity: identityForm.value.identity,
        remark: identityForm.value.remark
      };

      updateUserIdentity(identityForm.value.userId, data).then(response => {
        proxy.$modal.msgSuccess("身份修改成功");
        identityDialogVisible.value = false;
        getList(); // 刷新列表
      }).catch(error => {
        proxy.$modal.msgError("身份修改失败");
      }).finally(() => {
        identitySubmitting.value = false;
      });
    }
  });
}

/** 取消身份修改 */
function cancelIdentityForm() {
  identityDialogVisible.value = false;
  identityForm.value = {
    userId: undefined,
    username: '',
    nickname: '',
    phone: '',
    currentIdentity: undefined,
    identity: undefined,
    remark: ''
  };
}

/** 查看下级玩家列表 */
function handleViewSubordinates(row) {
  if (!row.inviteCode) {
    proxy.$modal.msgWarning("该用户没有邀请码");
    return;
  }

  currentInviter.value = {
    id: row.id,              // 保存用户ID用于分页
    username: row.username,
    nickname: row.nickname,
    inviteCode: row.inviteCode
  };

  subordinatesQueryParams.value.pageNum = 1;
  subordinatesDialogVisible.value = true;
  getSubordinatesList(row.id);
}

/** 获取下级玩家列表 */
function getSubordinatesList(userId) {
  if (!userId) {
    proxy.$modal.msgError("用户ID不能为空");
    return;
  }

  subordinatesLoading.value = true;
  console.log('获取下级玩家列表 - 用户ID:', userId, '查询参数:', subordinatesQueryParams.value);

  getSubordinateUsers(userId, subordinatesQueryParams.value).then(response => {
    console.log('下级玩家列表响应:', response);
    subordinatesList.value = response.rows;
    subordinatesTotal.value = response.total;
  }).catch(error => {
    console.error('获取下级玩家列表失败:', error);
    proxy.$modal.msgError("获取下级玩家列表失败: " + (error.message || '未知错误'));
  }).finally(() => {
    subordinatesLoading.value = false;
  });
}

/** 下级玩家列表分页 */
function handleSubordinatesPagination(pagination) {
  subordinatesQueryParams.value.pageNum = pagination.page;
  subordinatesQueryParams.value.pageSize = pagination.limit;

  // 直接使用保存的用户ID，避免复杂的查找逻辑
  if (currentInviter.value.id) {
    getSubordinatesList(currentInviter.value.id);
  } else {
    console.error('无法获取当前用户ID，分页操作失败');
    proxy.$modal.msgError('分页操作失败，请重新打开下级玩家列表');
  }
}

/** 查看用户订单明细 */
function handleViewUserOrders(row) {
  currentOrderUser.value = row;
  userOrdersDialogVisible.value = true;
  activeOrderTab.value = 'box';
  // 默认加载消费订单
  getBoxOrdersList(row.id);
}

/** 订单标签页切换 */
function handleOrderTabClick(tab) {
  console.log('订单标签页切换:', tab.name, '当前用户:', currentOrderUser.value);

  if (tab.name === 'box') {
    console.log('切换到消费订单标签页');
    getBoxOrdersList(currentOrderUser.value.id);
  } else if (tab.name === 'recharge') {
    console.log('切换到充值订单标签页');
    getRechargeOrdersList(currentOrderUser.value.id);
  }
}

/** 获取消费订单列表 */
function getBoxOrdersList(userId) {
  boxOrdersLoading.value = true;

  getUserConsumeList(userId, boxOrdersQueryParams.value).then(response => {
    boxOrdersList.value = response.rows;
    boxOrdersTotal.value = response.total;
  }).catch(error => {
    proxy.$modal.msgError("获取消费订单列表失败");
  }).finally(() => {
    boxOrdersLoading.value = false;
  });
}

/** 获取充值订单列表 */
function getRechargeOrdersList(userId) {
  console.log('开始获取充值订单列表，用户ID:', userId);
  console.log('查询参数:', rechargeOrdersQueryParams.value);

  rechargeOrdersLoading.value = true;

  getUserRechargeList(userId, rechargeOrdersQueryParams.value).then(response => {
    console.log('充值订单API响应:', response);
    rechargeOrdersList.value = response.rows || [];
    rechargeOrdersTotal.value = response.total || 0;
    proxy.$modal.msgSuccess(`成功获取${rechargeOrdersList.value.length}条充值订单记录`);
  }).catch(error => {
    console.error('获取充值订单列表失败:', error);
    proxy.$modal.msgError("获取充值订单列表失败: " + (error.message || error));
  }).finally(() => {
    rechargeOrdersLoading.value = false;
  });
}

/** 消费订单分页 */
function handleBoxOrdersPagination(pagination) {
  boxOrdersQueryParams.value.pageNum = pagination.page;
  boxOrdersQueryParams.value.pageSize = pagination.limit;
  getBoxOrdersList(currentOrderUser.value.id);
}

/** 充值订单分页 */
function handleRechargeOrdersPagination(pagination) {
  rechargeOrdersQueryParams.value.pageNum = pagination.page;
  rechargeOrdersQueryParams.value.pageSize = pagination.limit;
  getRechargeOrdersList(currentOrderUser.value.id);
}

// 在组件挂载时获取列表
getList();
</script>

<style scoped>
.dialog-footer {
  text-align: center;
  margin-top: 20px;
}

/* 背包样式 */
.bag-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
}

.user-level, .user-coin {
  font-size: 14px;
  color: #606266;
  margin-top: 5px;
}

.user-coin {
  color: #e6a23c;
}

.bag-count {
  background-color: #f0f9eb;
  color: #67c23a;
  padding: 5px 10px;
  border-radius: 4px;
}

.bag-container {
  margin-top: 20px;
}

.bag-item-card {
  margin-bottom: 15px;
  transition: transform 0.3s;
  height: 180px;
}

.bag-item-card:hover {
  transform: translateY(-5px);
}

.bag-item-image {
  text-align: center;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-image {
  max-height: 100%;
  max-width: 100%;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  background-color: #f5f7fa;
}

.bag-item-info {
  margin-top: 8px;
  text-align: center;
}

.item-name {
  font-weight: bold;
  margin-bottom: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
}

.item-rarity {
  margin-bottom: 3px;
  font-size: 10px;
  border-radius: 10px;
  padding: 1px 6px;
  display: inline-block;
}

.rarity-1 {
  background-color: #f5f5f5;
  color: #909399;
}

.rarity-2 {
  background-color: #e8f4ff;
  color: #409eff;
}

.rarity-3 {
  background-color: #ebfff0;
  color: #67c23a;
}

.rarity-4 {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.rarity-5 {
  background-color: #fef0f0;
  color: #f56c6c;
}

/* 标签样式 */
.rarity-崭新出厂 {
  background-color: #b8f2e6;
  color: #17a2b8;
}

.rarity-略有磨损 {
  background-color: #d4edda;
  color: #28a745;
}

.rarity-久经沙场 {
  background-color: #fff3cd;
  color: #ffc107;
}

.rarity-破损不堪 {
  background-color: #f8d7da;
  color: #dc3545;
}

.rarity-战痕累累 {
  background-color: #d6d8d9;
  color: #6c757d;
}

.item-price {
  font-size: 11px;
  color: #e6a23c;
  margin-bottom: 2px;
}

.item-count {
  font-size: 11px;
  color: #606266;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
  padding-bottom: 10px;
}

/* 搜索和排序工具栏样式 */
.bag-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 0 5px;
}

.search-input {
  width: 60%;
}

.sort-select {
  width: 35%;
}

.bag-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 10px;
}

.bag-content {
  margin-top: 10px;
}

/* IP地址标签样式 */
.ip-tag {
  margin-left: 5px;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  padding: 0 4px;
}

/* 🆕 首充时间相关样式 */
.first-recharge-time {
  color: #67C23A;
  font-weight: 500;
}

.no-recharge {
  color: #909399;
  font-style: italic;
  font-size: 12px;
}

/* 地理位置信息样式 */
.ip-location {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 操作列优化样式 */
.el-table .small-padding .cell {
  padding: 8px 0;
}

.el-table .fixed-width .cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: nowrap;
}

/* 下拉菜单按钮样式 */
.el-dropdown .el-button--small {
  padding: 4px 8px;
  font-size: 12px;
}

/* 确保操作按钮在同一行 */
.el-table .fixed-width .el-button {
  margin: 0 2px;
  white-space: nowrap;
}
</style>
