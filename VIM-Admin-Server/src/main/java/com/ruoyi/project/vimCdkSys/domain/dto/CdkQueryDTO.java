package com.ruoyi.project.vimCdkSys.domain.dto;

import lombok.Data;

/**
 * CDK查询数据传输对象
 */
@Data
public class CdkQueryDTO {
    /**
     * CDK码
     */
    private String cdk;
    
    /**
     * 类型（1：电能 2：钥匙 3：物品）
     */
    private Integer type;
    
    /**
     * 状态（0：未兑换 1：已兑换）
     */
    private Integer state;

    /**
     * CDK信息类型（幸运id、全补、半补、转发长智）
     */
    private String info;

    /**
     * 开始日期
     */
    private String startDate;
    
    /**
     * 结束日期
     */
    private String endDate;

    /**
     * CDK码列表（用于导出指定CDK）
     */
    private String cdkCodes;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 页码（前端兼容字段）
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 获取页码，优先使用pageNum
     */
    public Integer getPage() {
        return pageNum != null ? pageNum : page;
    }

    @Override
    public String toString() {
        return "CdkQueryDTO{" +
                "cdk='" + cdk + '\'' +
                ", type=" + type +
                ", state=" + state +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", cdkCodes='" + cdkCodes + '\'' +
                ", page=" + page +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}