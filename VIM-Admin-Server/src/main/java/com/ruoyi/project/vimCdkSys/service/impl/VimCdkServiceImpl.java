package com.ruoyi.project.vimCdkSys.service.impl;

import com.ruoyi.project.vimCdkSys.domain.VimCdk;
import com.ruoyi.project.vimCdkSys.domain.dto.CdkBatchDTO;
import com.ruoyi.project.vimCdkSys.domain.dto.CdkDTO;
import com.ruoyi.project.vimCdkSys.domain.dto.CdkExchangeDTO;
import com.ruoyi.project.vimCdkSys.domain.dto.CdkExchangeDetailQueryDTO;
import com.ruoyi.project.vimCdkSys.domain.dto.CdkQueryDTO;
import com.ruoyi.project.vimCdkSys.domain.dto.UserSearchDTO;
import com.ruoyi.project.vimCdkSys.domain.dto.UserCdkExchangeQueryDTO;
import com.ruoyi.project.vimCdkSys.domain.vo.CdkExchangeVO;
import com.ruoyi.project.vimCdkSys.domain.vo.CdkVO;
import com.ruoyi.project.vimCdkSys.domain.vo.CdkExchangeDetailVO;
import com.ruoyi.project.vimCdkSys.domain.vo.UserSelectVO;
import com.ruoyi.project.vimCdkSys.domain.vo.UserCdkExchangeVO;
import com.ruoyi.project.vimCdkSys.domain.vo.UserDetailInfoVO;
import com.ruoyi.project.vimCdkSys.domain.dto.CdkInfoSnapshotDTO;
import com.ruoyi.project.vimCdkSys.exception.CdkException;
import com.ruoyi.project.vimCdkSys.mapper.VimCdkMapper;
import com.ruoyi.project.vimCdkSys.service.VimCdkService;
import com.ruoyi.project.vimCdkSys.utils.CdkGenerator;
import com.ruoyi.project.vimCdkSys.utils.PageResult;
import com.ruoyi.framework.aspectj.lang.annotation.DataScope;
import com.alibaba.fastjson2.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * CDK服务实现类
 */
@Service
public class VimCdkServiceImpl implements VimCdkService {

    private static final Logger log = LoggerFactory.getLogger(VimCdkServiceImpl.class);

    @Autowired
    private VimCdkMapper vimCdkMapper;
    /**
     * CDK长度
     */
    private static final int CDK_LENGTH = 16;
    /**
     * 生成单个CDK
     */
    @Override
    @Transactional
    public String generateCdk(CdkDTO cdkDTO) {
        // 检查参数
        if (cdkDTO.getType() < 1 || cdkDTO.getType() > 3) {
            throw new CdkException("CDK类型错误，只能是1-3之间的整数");
        }
        
        // 如果提供了自定义CDK码，则检查是否已存在
        if (cdkDTO.getCdk() != null && !cdkDTO.getCdk().isEmpty()) {
            VimCdk existCdk = vimCdkMapper.selectCdkByCdkCode(cdkDTO.getCdk());
            if (existCdk != null) {
                throw new CdkException("CDK码已存在，请更换");
            }
            return saveCdk(cdkDTO.getCdk(), cdkDTO.getType(), cdkDTO.getValue(), cdkDTO.getForuser(), cdkDTO.getInfo());
        }
        
        // 生成唯一CDK码
        String cdkCode;
        boolean isUnique;
        do {
            cdkCode = CdkGenerator.generateFormattedCdk(CDK_LENGTH);
            VimCdk existCdk = vimCdkMapper.selectCdkByCdkCode(cdkCode);
            isUnique = (existCdk == null);
        } while (!isUnique);
        
        // 保存CDK
        return saveCdk(cdkCode, cdkDTO.getType(), cdkDTO.getValue(), cdkDTO.getForuser(), cdkDTO.getInfo());
    }
    
    /**
     * 批量生成CDK
     */
    @Override
    @Transactional
    public List<String> batchGenerateCdk(CdkBatchDTO batchDTO) {
        // 检查参数
        if (batchDTO.getType() < 1 || batchDTO.getType() > 3) {
            throw new CdkException("CDK类型错误，只能是1-3之间的整数");
        }
        
        if (batchDTO.getCount() <= 0 || batchDTO.getCount() > 1000) {
            throw new CdkException("生成数量必须在1-1000之间");
        }
        
        // 生成唯一CDK码集合
        Set<String> cdkCodes = CdkGenerator.generateUniqueCdks(batchDTO.getCount(), CDK_LENGTH);
        
        // 检查数据库中是否已存在，过滤掉已存在的
        List<String> finalCdkList = new ArrayList<>();
        for (String cdkCode : cdkCodes) {
            VimCdk existCdk = vimCdkMapper.selectCdkByCdkCode(cdkCode);
            if (existCdk == null) {
                finalCdkList.add(cdkCode);
            }
        }
        
        // 如果过滤后的列表数量不足，补充生成
        while (finalCdkList.size() < batchDTO.getCount()) {
            String cdkCode = CdkGenerator.generateCdk(CDK_LENGTH);
            VimCdk existCdk = vimCdkMapper.selectCdkByCdkCode(cdkCode);
            if (existCdk == null && !finalCdkList.contains(cdkCode)) {
                finalCdkList.add(cdkCode);
            }
        }
        
        // 获取用户详细信息并生成快照
        String infoSnapshot = null;
        if (batchDTO.getForuser() != null && batchDTO.getForuser() > 0) {
            try {
                UserDetailInfoVO userDetailInfo = getUserDetailInfo(batchDTO.getForuser());
                CdkInfoSnapshotDTO snapshot = CdkInfoSnapshotDTO.fromUserDetailInfo(
                    batchDTO.getInfo(), userDetailInfo);
                infoSnapshot = JSON.toJSONString(snapshot);

                // 检查JSON长度，如果过长则使用简化版本
                if (infoSnapshot.length() > 60000) { // TEXT字段最大约65535字符
                    infoSnapshot = createSimplifiedSnapshot(batchDTO.getInfo(), userDetailInfo);
                }
            } catch (Exception e) {
                // 如果获取用户信息失败，使用简单的info信息
                infoSnapshot = batchDTO.getInfo() != null ? batchDTO.getInfo() : "未知";
                log.warn("获取用户详细信息失败，使用简化信息: {}", e.getMessage());
            }
        } else {
            infoSnapshot = batchDTO.getInfo() != null ? batchDTO.getInfo() : "未知";
        }

        // 批量保存CDK
        final String finalInfoSnapshot = infoSnapshot;
        List<VimCdk> cdkList = finalCdkList.stream().map(cdkCode -> {
            VimCdk cdk = new VimCdk();
            cdk.setId(generateId());
            cdk.setCdk(cdkCode);
            cdk.setType(batchDTO.getType());
            cdk.setValue(batchDTO.getValue());
            cdk.setState(0); // 未兑换
            cdk.setCreateTime((int) (System.currentTimeMillis() / 1000));
            cdk.setUseTime(0);
            cdk.setUseUser(null);
            cdk.setForuser(batchDTO.getForuser());
            cdk.setInfo(finalInfoSnapshot);
            return cdk;
        }).collect(Collectors.toList());
        
        vimCdkMapper.batchInsertCdk(cdkList);
        
        return finalCdkList;
    }

    /**
     * 生成单个CDK并返回详细信息
     */
    @Override
    @Transactional
    public CdkVO generateCdkWithDetails(CdkDTO cdkDTO) {
        String cdkCode = generateCdk(cdkDTO);
        VimCdk cdk = vimCdkMapper.selectCdkByCdkCode(cdkCode);
        return convertToCdkVO(cdk);
    }

    /**
     * 批量生成CDK并返回详细信息
     */
    @Override
    @Transactional
    public List<CdkVO> batchGenerateCdkWithDetails(CdkBatchDTO batchDTO) {
        List<String> cdkCodes = batchGenerateCdk(batchDTO);
        List<CdkVO> cdkVOList = new ArrayList<>();

        for (String cdkCode : cdkCodes) {
            VimCdk cdk = vimCdkMapper.selectCdkByCdkCode(cdkCode);
            if (cdk != null) {
                cdkVOList.add(convertToCdkVO(cdk));
            }
        }

        return cdkVOList;
    }

    /**
     * 将VimCdk转换为CdkVO
     */
    private CdkVO convertToCdkVO(VimCdk cdk) {
        CdkVO cdkVO = new CdkVO();
        cdkVO.setId(cdk.getId());
        cdkVO.setCdk(cdk.getCdk());
        cdkVO.setType(cdk.getType());
        cdkVO.setValue(cdk.getValue());
        cdkVO.setState(cdk.getState());

        // 设置类型名称
        switch (cdk.getType()) {
            case 1:
                cdkVO.setTypeName("电能");
                break;
            case 2:
                cdkVO.setTypeName("钥匙");
                break;
            case 3:
                cdkVO.setTypeName("物品");
                break;
            default:
                cdkVO.setTypeName("未知");
                break;
        }

        // 设置状态名称
        switch (cdk.getState()) {
            case 0:
                cdkVO.setStateName("未兑换");
                break;
            case 1:
                cdkVO.setStateName("已兑换");
                break;
            default:
                cdkVO.setStateName("未知");
                break;
        }

        // 设置时间
        if (cdk.getCreateTime() != null && cdk.getCreateTime() > 0) {
            cdkVO.setCreateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .format(new java.util.Date(cdk.getCreateTime() * 1000L)));
        }

        if (cdk.getUseTime() != null && cdk.getUseTime() > 0) {
            cdkVO.setUseTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .format(new java.util.Date(cdk.getUseTime() * 1000L)));
        } else {
            cdkVO.setUseTime("");
        }

        return cdkVO;
    }
    
    /**
     * 查询CDK列表
     */
    @Override
    public PageResult<CdkVO> getCdkList(CdkQueryDTO queryDTO) {
        // 计算分页参数
        Integer offset = (queryDTO.getPage() - 1) * queryDTO.getPageSize();
        Integer limit = queryDTO.getPageSize();

        // 查询CDK列表
        List<CdkVO> list = vimCdkMapper.selectCdkList(
                queryDTO.getCdk(), queryDTO.getType(), queryDTO.getState(), queryDTO.getInfo(),
                queryDTO.getStartDate(), queryDTO.getEndDate(), queryDTO.getCdkCodes(),
                offset, limit);

        // 查询总数
        int total = vimCdkMapper.selectCdkCount(
                queryDTO.getCdk(), queryDTO.getType(), queryDTO.getState(), queryDTO.getInfo(),
                queryDTO.getStartDate(), queryDTO.getEndDate(), queryDTO.getCdkCodes());

        // 返回分页结果
        return PageResult.build(list, total, queryDTO.getPage(), queryDTO.getPageSize());
    }
    
    /**
     * 兑换CDK
     */
    @Override
    @Transactional
    public CdkExchangeVO exchangeCdk(CdkExchangeDTO exchangeDTO) {
        CdkExchangeVO result = new CdkExchangeVO();
        result.setSuccess(false);
        
        // 查询CDK是否存在
        VimCdk cdk = vimCdkMapper.selectCdkByCdkCode(exchangeDTO.getCdk());
        if (cdk == null) {
            result.setMessage("CDK不存在");
            return result;
        }
        
        // 检查CDK是否已兑换
        if (cdk.getState() == 1) {
            result.setMessage("CDK已被兑换");
            return result;
        }
        
        // 更新CDK状态为已兑换
        int currentTime = (int) (System.currentTimeMillis() / 1000);
        int rows = vimCdkMapper.updateCdkState(cdk.getId(), 1, currentTime);
        if (rows <= 0) {
            result.setMessage("兑换失败，请重试");
            return result;
        }
        
        // 根据CDK类型执行不同的兑换逻辑
        switch (cdk.getType()) {
            case 1:
                // 电能兑换逻辑
                result.setTypeName("电能");
                // TODO: 调用电能增加接口
                break;
            case 2:
                // 钥匙兑换逻辑
                result.setTypeName("钥匙");
                // TODO: 调用钥匙增加接口
                break;
            case 3:
                // 物品兑换逻辑
                result.setTypeName("物品");
                // TODO: 调用物品发放接口
                break;
            default:
                result.setMessage("未知的CDK类型");
                return result;
        }
        
        // 设置兑换成功信息
        result.setSuccess(true);
        result.setType(cdk.getType());
        result.setValue(cdk.getValue());
        
        return result;
    }
    
    /**
     * 删除CDK
     */
    @Override
    public boolean deleteCdk(Integer id) {
        return vimCdkMapper.deleteCdkById(id) > 0;
    }
    
    /**
     * 批量删除CDK
     */
    @Override
    public boolean batchDeleteCdk(Integer[] ids) {
        return vimCdkMapper.batchDeleteCdk(ids) > 0;
    }

    /**
     * 查询CDK兑换详情
     */
    @Override
    public PageResult<CdkExchangeDetailVO> getCdkExchangeDetailList(CdkExchangeDetailQueryDTO queryDTO) {
        // 计算分页参数
        Integer offset = (queryDTO.getPage() - 1) * queryDTO.getPageSize();
        Integer limit = queryDTO.getPageSize();

        // 查询兑换详情列表
        List<CdkExchangeDetailVO> list = vimCdkMapper.selectCdkExchangeDetailList(
                queryDTO.getCdk(), offset, limit);

        // 查询总数
        int total = vimCdkMapper.selectCdkExchangeDetailCount(queryDTO.getCdk());

        // 返回分页结果
        return PageResult.build(list, total, queryDTO.getPage(), queryDTO.getPageSize());
    }
    
    /**
     * 保存CDK
     */
    private String saveCdk(String cdkCode, Integer type, java.math.BigDecimal value, Integer foruser, String cdkInfoType) {
        // 获取用户详细信息并生成快照
        String infoSnapshot = null;
        if (foruser != null && foruser > 0) {
            try {
                UserDetailInfoVO userDetailInfo = getUserDetailInfo(foruser);
                CdkInfoSnapshotDTO snapshot = CdkInfoSnapshotDTO.fromUserDetailInfo(cdkInfoType, userDetailInfo);
                infoSnapshot = JSON.toJSONString(snapshot);

                // 检查JSON长度，如果过长则使用简化版本
                if (infoSnapshot.length() > 60000) { // TEXT字段最大约65535字符
                    infoSnapshot = createSimplifiedSnapshot(cdkInfoType, userDetailInfo);
                }
            } catch (Exception e) {
                // 如果获取用户信息失败，使用简单的info信息
                infoSnapshot = cdkInfoType != null ? cdkInfoType : "未知";
                log.warn("获取用户详细信息失败，使用简化信息: {}", e.getMessage());
            }
        } else {
            infoSnapshot = cdkInfoType != null ? cdkInfoType : "未知";
        }

        VimCdk cdk = new VimCdk();
        cdk.setId(generateId());
        cdk.setCdk(cdkCode);
        cdk.setType(type);
        cdk.setValue(value);
        cdk.setState(0); // 未兑换
        cdk.setCreateTime((int) (System.currentTimeMillis() / 1000));
        cdk.setUseTime(0);
        cdk.setUseUser(null);
        cdk.setForuser(foruser);
        cdk.setInfo(infoSnapshot);

        vimCdkMapper.insertCdk(cdk);
        return cdkCode;
    }
    
    /**
     * 搜索用户列表（用于CDK用户选择）
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "su")
    public PageResult<UserSelectVO> searchUsers(UserSearchDTO searchDTO) {
        // 计算偏移量
        int offset = (searchDTO.getPage() - 1) * searchDTO.getPageSize();

        // 查询用户列表
        List<UserSelectVO> userList = vimCdkMapper.searchUsers(
            searchDTO,
            offset,
            searchDTO.getPageSize()
        );
        // 查询总数
        int total = vimCdkMapper.searchUsersCount(searchDTO);
        return new PageResult<>(userList, total);
    }

    /**
     * 查询用户CDK兑换记录
     */
    @Override
    public PageResult<UserCdkExchangeVO> getUserCdkExchangeRecords(UserCdkExchangeQueryDTO queryDTO) {
        // 计算偏移量
        int offset = (queryDTO.getPage() - 1) * queryDTO.getPageSize();

        // 查询用户CDK兑换记录列表
        List<UserCdkExchangeVO> recordList = vimCdkMapper.selectUserCdkExchangeRecords(
            queryDTO.getUserId(),
            queryDTO.getStartDate(),
            queryDTO.getEndDate(),
            queryDTO.getCdkType(),
            offset,
            queryDTO.getPageSize()
        );

        // 查询总数
        int total = vimCdkMapper.selectUserCdkExchangeRecordsCount(
            queryDTO.getUserId(),
            queryDTO.getStartDate(),
            queryDTO.getEndDate(),
            queryDTO.getCdkType()
        );

        return PageResult.build(recordList, total, queryDTO.getPage(), queryDTO.getPageSize());
    }

    /**
     * 查询用户详细信息（包含钥匙数量、余额、背包价值）
     */
    @Override
    public UserDetailInfoVO getUserDetailInfo(Integer userId) {
        if (userId == null) {
            throw new CdkException("用户ID不能为空");
        }

        UserDetailInfoVO userDetailInfo = vimCdkMapper.selectUserDetailInfo(userId);
        if (userDetailInfo == null) {
            throw new CdkException("用户不存在或已被禁用");
        }

        return userDetailInfo;
    }

    /**
     * 创建简化的用户信息快照
     */
    private String createSimplifiedSnapshot(String cdkInfoType, UserDetailInfoVO userDetailInfo) {
        try {
            Map<String, Object> simplifiedSnapshot = new HashMap<>();
            simplifiedSnapshot.put("cdkInfoType", cdkInfoType);
            simplifiedSnapshot.put("snapshotTime", java.time.LocalDateTime.now().toString());
            simplifiedSnapshot.put("userId", userDetailInfo.getId());
            simplifiedSnapshot.put("nickname", userDetailInfo.getNickname());
            simplifiedSnapshot.put("level", userDetailInfo.getLevel());
            simplifiedSnapshot.put("identity", userDetailInfo.getIdentityName());
            simplifiedSnapshot.put("balance", userDetailInfo.getBalance());
            simplifiedSnapshot.put("keyAmount", userDetailInfo.getKeyAmount());
            simplifiedSnapshot.put("backpackValue", userDetailInfo.getBackpackValue());
            simplifiedSnapshot.put("superiorNickname", userDetailInfo.getSuperiorNickname());

            return JSON.toJSONString(simplifiedSnapshot);
        } catch (Exception e) {
            // 如果简化版本也失败，返回最基本的信息
            return String.format("{\"cdkInfoType\":\"%s\",\"userId\":%d,\"nickname\":\"%s\"}",
                    cdkInfoType, userDetailInfo.getId(), userDetailInfo.getNickname());
        }
    }

    /**
     * 生成主键ID
     */
    private Integer generateId() {
        // 这里使用时间戳加随机数的方式生成ID
        // 实际项目中可能需要使用更复杂的ID生成策略
        return (int) (System.currentTimeMillis() / 1000) + (int) (Math.random() * 10000);
    }
}