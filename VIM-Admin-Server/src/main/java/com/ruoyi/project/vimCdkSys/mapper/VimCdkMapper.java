package com.ruoyi.project.vimCdkSys.mapper;

import com.ruoyi.project.vimCdkSys.domain.VimCdk;
import com.ruoyi.project.vimCdkSys.domain.dto.UserSearchDTO;
import com.ruoyi.project.vimCdkSys.domain.vo.CdkVO;
import com.ruoyi.project.vimCdkSys.domain.vo.CdkExchangeDetailVO;
import com.ruoyi.project.vimCdkSys.domain.vo.UserSelectVO;
import com.ruoyi.project.vimCdkSys.domain.vo.UserCdkExchangeVO;
import com.ruoyi.project.vimCdkSys.domain.vo.UserDetailInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CDK Mapper接口
 */
@Mapper
public interface VimCdkMapper {
    /**
     * 查询CDK列表
     */
    List<CdkVO> selectCdkList(@Param("cdk") String cdk,
                             @Param("type") Integer type,
                             @Param("state") Integer state,
                             @Param("info") String info,
                             @Param("startDate") String startDate,
                             @Param("endDate") String endDate,
                             @Param("cdkCodes") String cdkCodes,
                             @Param("offset") Integer offset,
                             @Param("limit") Integer limit);

    /**
     * 查询CDK总数
     */
    int selectCdkCount(@Param("cdk") String cdk,
                       @Param("type") Integer type,
                       @Param("state") Integer state,
                       @Param("info") String info,
                       @Param("startDate") String startDate,
                       @Param("endDate") String endDate,
                       @Param("cdkCodes") String cdkCodes);
    
    /**
     * 根据CDK码查询
     */
    VimCdk selectCdkByCdkCode(@Param("cdk") String cdk);
    
    /**
     * 插入CDK记录
     */
    int insertCdk(VimCdk cdk);
    
    /**
     * 批量插入CDK
     */
    int batchInsertCdk(List<VimCdk> cdkList);
    
    /**
     * 更新CDK状态
     */
    int updateCdkState(@Param("id") Integer id, @Param("state") Integer state, @Param("useTime") Integer useTime);
    
    /**
     * 根据ID查询CDK
     */
    VimCdk selectCdkById(@Param("id") Integer id);
    
    /**
     * 删除CDK
     */
    int deleteCdkById(@Param("id") Integer id);
    
    /**
     * 批量删除CDK
     */
    int batchDeleteCdk(Integer[] ids);

    /**
     * 查询CDK兑换详情列表
     */
    List<CdkExchangeDetailVO> selectCdkExchangeDetailList(@Param("cdk") String cdk,
                                                          @Param("offset") Integer offset,
                                                          @Param("limit") Integer limit);

    /**
     * 查询CDK兑换详情总数
     */
    int selectCdkExchangeDetailCount(@Param("cdk") String cdk);

    /**
     * 搜索用户列表（用于CDK用户选择）
     */
    List<UserSelectVO> searchUsers(@Param("searchDTO") UserSearchDTO searchDTO,
                                   @Param("offset") Integer offset,
                                   @Param("limit") Integer limit);

    /**
     * 搜索用户总数
     */
    int searchUsersCount(@Param("searchDTO") UserSearchDTO searchDTO);

    /**
     * 查询用户CDK兑换记录列表
     */
    List<UserCdkExchangeVO> selectUserCdkExchangeRecords(@Param("userId") Integer userId,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate,
                                                         @Param("cdkType") Integer cdkType,
                                                         @Param("offset") Integer offset,
                                                         @Param("limit") Integer limit);

    /**
     * 查询用户CDK兑换记录总数
     */
    int selectUserCdkExchangeRecordsCount(@Param("userId") Integer userId,
                                          @Param("startDate") String startDate,
                                          @Param("endDate") String endDate,
                                          @Param("cdkType") Integer cdkType);

    /**
     * 查询用户详细信息（包含钥匙数量、余额、背包价值）
     */
    UserDetailInfoVO selectUserDetailInfo(@Param("userId") Integer userId);
}