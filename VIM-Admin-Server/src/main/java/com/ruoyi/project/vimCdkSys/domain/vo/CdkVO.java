package com.ruoyi.project.vimCdkSys.domain.vo;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * CDK视图对象
 */
@Data
public class CdkVO {
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * CDK码
     */
    @Excel(name = "CDK码")
    private String cdk;

    /**
     * 类型（1：电能 2：钥匙 3：物品）
     */
    private Integer type;

    /**
     * 类型名称
     */
    @Excel(name = "类型")
    private String typeName;

    /**
     * 数量或物品ID
     */
    @Excel(name = "数值")
    private BigDecimal value;

    /**
     * 状态（0：未兑换 1：已兑换）
     */
    private Integer state;

    /**
     * 状态名称
     */
    @Excel(name = "状态")
    private String stateName;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    /**
     * 兑换时间
     */
    @Excel(name = "兑换时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String useTime;

    /**
     * 指定兑换用户ID
     */
    private Integer foruserId;

    /**
     * 指定兑换用户昵称
     */
    @Excel(name = "绑定用户")
    private String foruserNickname;

    /**
     * 指定兑换用户手机号
     */
    private String foruserPhone;

    /**
     * CDK信息类型（幸运id、全补、半补、转发长智）
     */
    private String info;
}