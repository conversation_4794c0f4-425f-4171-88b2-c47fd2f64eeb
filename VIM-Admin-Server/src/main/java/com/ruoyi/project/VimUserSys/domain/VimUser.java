package com.ruoyi.project.VimUserSys.domain;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 盲盒用户对象 vim_user
 * 
 * <AUTHOR> and 催一催
 * @date 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "VimUser", description = "盲盒用户信息对象")
public class VimUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户id
 */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long id;

    /** 手机号码 */
    @ApiModelProperty(value = "手机号码", example = "13800138000")
    @Excel(name = "手机号码")
    private String phone;

    /** 密码 */
    @ApiModelProperty(value = "密码", example = "******", notes = "密码将被加密存储")
    @Excel(name = "密码")
    private String password;

    /** 昵称 */
    @ApiModelProperty(value = "昵称", example = "盲盒爱好者")
    @Excel(name = "昵称")
    private String nickname;

    /** 用户名 */
    @ApiModelProperty(value = "用户名", example = "user123")
    @Excel(name = "用户名")
    private String username;

    /** 用户头像 */
    @ApiModelProperty(value = "用户头像", example = "http://example.com/avatar.jpg")
    @Excel(name = "用户头像")
    private String userimage;

    /** 最后一次登录时间 */
    @ApiModelProperty(value = "最后一次登录时间", example = "1648738800000")
    @Excel(name = "最后一次登录时间")
    private Long lastLoginTime;

    /** 最后一次登录ip */
    @ApiModelProperty(value = "最后一次登录IP", example = "***********")
    @Excel(name = "最后一次登录ip")
    private String lastLoginIp;

    /** 当前电能 */
    @ApiModelProperty(value = "当前电能余额", example = "100.50")
    @Excel(name = "当前电能")
    private BigDecimal coin;

    /** 当前钥匙 */
    @ApiModelProperty(value = "当前钥匙", example = "100.50")
    @Excel(name = "当前钥匙")
    private BigDecimal key;

    /** 邀请码 */
    @ApiModelProperty(value = "邀请码", example = "INVITE123")
    @Excel(name = "邀请码")
    private String inviteCode;

    /** 邀请人ID */
    @ApiModelProperty(value = "邀请人ID", example = "1001", notes = "当前绑定的邀请用户ID")
    @Excel(name = "邀请人ID")
    private Long inviteUser;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "1648738800000")
    @Excel(name = "创建时间")
    private Long vimUsercreateTime;

    /** steamid */
    @ApiModelProperty(value = "Steam ID", example = "76561198123456789")
    @Excel(name = "steamid")
    private String steamId;

    /** 提货链接 */
    @ApiModelProperty(value = "提货链接", example = "https://steamcommunity.com/tradeoffer/...")
    @Excel(name = "提货链接")
    private String steamLink;

    /** 经验 */
    @ApiModelProperty(value = "经验值", example = "1000")
    @Excel(name = "经验")
    private Long exp;

    /** 等级 */
    @ApiModelProperty(value = "用户等级", example = "5")
    @Excel(name = "等级")
    private Long level;

    /** 客户端种子 */
    @ApiModelProperty(value = "客户端种子", example = "abcdef1234567890")
    @Excel(name = "客户端种子")
    private String seed;
    
    /** 用户身份类型 */
    @ApiModelProperty(value = "用户身份类型", example = "1", notes = "1-普通用户,2-线上主播,3-线下主播,4-代理")
    @Excel(name = "用户身份类型")
    private Integer identity;

    /** 是否实名 */
    @ApiModelProperty(value = "是否实名", example = "1", notes = "1-已实名,0-未实名")
    @Excel(name = "是否实名")
    private Integer isauth;
    
    /** 账号状态 */
    @ApiModelProperty(value = "账号状态", example = "1", notes = "1-正常,2-禁用")
    @Excel(name = "账号状态")
    private Integer state;

    /** 首次充值时间（时间戳） */
    @ApiModelProperty(value = "首次充值时间", example = "1648738800")
    private Long firstRechargeTime;

    /** 注册IP地址 */
    @ApiModelProperty(value = "注册IP地址", example = "***********")
    @Excel(name = "注册IP地址")
    private String createIp;

    /** 首次充值时间（格式化字符串） */
    @ApiModelProperty(value = "首次充值时间字符串", example = "2022-03-31 20:00:00")
    private String firstRechargeTimeStr;
}
