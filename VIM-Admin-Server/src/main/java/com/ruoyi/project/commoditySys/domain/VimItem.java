package com.ruoyi.project.commoditySys.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 商品管理对象 vim_item
 * 
 * <AUTHOR> and 羊
 * @date 2025-03-13
 */
@ApiModel(value = "VimItem", description = "商品信息对象")
public class VimItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 物品id */
    @ApiModelProperty(value = "物品ID", example = "1")
    private Long id;

    /** 物品名称 */
    @ApiModelProperty(value = "物品名称", example = "神秘道具")
    @Excel(name = "物品名称")
    private String name;
    /** 物品英文名称 */
    @ApiModelProperty(value = "物品英文名称", example = "Mystery Item")
    @Excel(name = "物品英文名称")
    private String hashname;
    /** 物品标签 */
    @ApiModelProperty(value = "物品标签", example = "稀有,武器")
    @Excel(name = "物品标签")
    private String tag;

    /** 物品展示价 */
    @ApiModelProperty(value = "物品展示价", example = "199.99")
    @Excel(name = "物品展示价")
    private BigDecimal priceShow;

    /** 物品成本价 */
    @ApiModelProperty(value = "物品成本价", example = "99.99")
    @Excel(name = "物品成本价")
    private BigDecimal priceCost;

    /** 物品购买价 */
    @ApiModelProperty(value = "物品购买价", example = "149.99")
    @Excel(name = "物品购买价")
    private BigDecimal priceBuy;

    /** 物品回收价 */
    @ApiModelProperty(value = "物品回收价", example = "79.99")
    @Excel(name = "物品回收价")
    private BigDecimal priceRecycle;

    /** 物品图片 */
    @ApiModelProperty(value = "物品图片", example = "http://example.com/images/item.jpg")
    @Excel(name = "物品图片")
    private String image;

    /** 是否上架 */
    @ApiModelProperty(value = "是否上架", example = "1", notes = "0=下架，1=上架")
    @Excel(name = "是否上架")
    private Long sale;

    /** 物品库存 */
    @ApiModelProperty(value = "物品库存", example = "100")
    @Excel(name = "物品库存")
    private Long stock;

    /** 背包中物品数量（仅用于背包查询） */
    @ApiModelProperty(value = "背包中物品数量", example = "2")
    private Long count;

    // 修改后
    @ApiModelProperty(value = "创建时间戳", example = "1648738800000")
    private Long CreateTimeStamp;
    
    @ApiModelProperty(value = "更新时间戳", example = "1648738800000")
    private Long updateTimeStamp;
    
    public Long getUpdateTimeStamp() {
        return updateTimeStamp;
    }

    public void setUpdateTimeStamp(Long updateTimeStamp) {
        this.updateTimeStamp = updateTimeStamp;
    }
   // 新增方法来获取时间戳类型的 createTime
    public Long getCreateTimeStamp() {
        return CreateTimeStamp;
    }

    // 新增方法来设置时间戳类型的 createTime
    public void setCreateTimeStamp(Long createTime) {
        this.CreateTimeStamp = createTime;
    }
// 对应的 getter 和 setter 方法也要修改

    public void setCreateTime(Long createTime) {
        this.CreateTimeStamp = createTime;
    }



    /** 软删除时间 */
    @ApiModelProperty(value = "软删除时间", example = "0", notes = "0表示未删除，其他值为删除时的时间戳")
    private Long deleteTime;

    /** 参考价格 */
    @ApiModelProperty(value = "参考价格", example = "299.99", notes = "从cs2_skin_platform库获取的参考价格")
    private BigDecimal referencePrice;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setTag(String tag) 
    {
        this.tag = tag;
    }

    public String getTag() 
    {
        return tag;
    }

    public void setPriceShow(BigDecimal priceShow) 
    {
        this.priceShow = priceShow;
    }

    public BigDecimal getPriceShow() 
    {
        return priceShow;
    }

    public void setPriceCost(BigDecimal priceCost) 
    {
        this.priceCost = priceCost;
    }

    public BigDecimal getPriceCost() 
    {
        return priceCost;
    }

    public void setPriceBuy(BigDecimal priceBuy) 
    {
        this.priceBuy = priceBuy;
    }

    public BigDecimal getPriceBuy() 
    {
        return priceBuy;
    }

    public void setPriceRecycle(BigDecimal priceRecycle) 
    {
        this.priceRecycle = priceRecycle;
    }

    public BigDecimal getPriceRecycle() 
    {
        return priceRecycle;
    }

    public void setImage(String image) 
    {
        this.image = image;
    }

    public String getImage() 
    {
        return image;
    }

    public void setSale(Long sale) 
    {
        this.sale = sale;
    }

    public Long getSale() 
    {
        return sale;
    }

    public void setStock(Long stock) 
    {
        this.stock = stock;
    }

    public Long getStock()
    {
        return stock;
    }

    public void setCount(Long count)
    {
        this.count = count;
    }

    public Long getCount()
    {
        return count;
    }

    public void setDeleteTime(Long deleteTime)
    {
        this.deleteTime = deleteTime;
    }

    public Long getDeleteTime()
    {
        return deleteTime;
    }

    public void setHashname(String hashname) {
        this.hashname = hashname;
    }

    public String getHashname() {
        return hashname;
    }

    public void setReferencePrice(BigDecimal referencePrice) {
        this.referencePrice = referencePrice;
    }

    public BigDecimal getReferencePrice() {
        return referencePrice;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("tag", getTag())
            .append("priceShow", getPriceShow())
            .append("priceCost", getPriceCost())
            .append("priceBuy", getPriceBuy())
            .append("priceRecycle", getPriceRecycle())
            .append("image", getImage())
            .append("sale", getSale())
            .append("stock", getStock())
            .append("create_time", getCreateTime())
            .append("updateTimeStamp", getUpdateTimeStamp())
            .append("deleteTime", getDeleteTime())
            .append("hashname", getHashname())
            .toString();
    }
}
