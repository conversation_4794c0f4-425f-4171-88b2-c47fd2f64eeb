<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimCdkSys.mapper.VimCdkMapper">

    <!-- CDK结果映射 -->
    <resultMap id="CdkVOMap" type="com.ruoyi.project.vimCdkSys.domain.vo.CdkVO">
        <id column="id" property="id" />
        <result column="cdk" property="cdk" />
        <result column="type" property="type" />
        <result column="value" property="value" />
        <result column="state" property="state" />
        <result column="type_name" property="typeName" />
        <result column="state_name" property="stateName" />
        <result column="create_time" property="createTime" />
        <result column="use_time" property="useTime" />
        <result column="foruser_id" property="foruserId" />
        <result column="foruser_nickname" property="foruserNickname" />
        <result column="foruser_phone" property="foruserPhone" />
        <result column="info" property="info" />
    </resultMap>
    
    <!-- VimCdk结果映射 -->
    <resultMap id="VimCdkMap" type="com.ruoyi.project.vimCdkSys.domain.VimCdk">
        <id column="id" property="id" />
        <result column="cdk" property="cdk" />
        <result column="type" property="type" />
        <result column="value" property="value" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="use_time" property="useTime" />
        <result column="use_user" property="useUser" />
        <result column="foruser" property="foruser" />
        <result column="info" property="info" />
    </resultMap>
    
    <!-- 查询CDK列表 -->
    <!-- 查询CDK列表 -->
    <select id="selectCdkList" resultMap="CdkVOMap">
        SELECT
        c.id,
        c.cdk,
        c.type,
        c.value,
        c.state,
        c.foruser AS foruser_id,
        c.info,
        u.nickname AS foruser_nickname,
        u.phone AS foruser_phone,
        CASE
        WHEN c.type = 1 THEN '电能'
        WHEN c.type = 2 THEN '钥匙'
        WHEN c.type = 3 THEN '物品'
        ELSE '未知'
        END AS type_name,
        CASE
        WHEN c.state = 0 THEN '未兑换'
        WHEN c.state = 1 THEN '已兑换'
        ELSE '未知'
        END AS state_name,
        FROM_UNIXTIME(c.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
        CASE
        WHEN c.use_time IS NULL OR c.use_time = 0 THEN ''
        ELSE FROM_UNIXTIME(c.use_time, '%Y-%m-%d %H:%i:%s')
        END AS use_time
        FROM
        vim_cdk c
        LEFT JOIN vim_user u ON c.foruser = u.id
        <where>
            <!-- 添加一个默认为真但对业务有意义的条件 -->
            c.id IS NOT NULL
            <if test="cdk != null and cdk != ''">
                AND c.cdk LIKE CONCAT('%', #{cdk}, '%')
            </if>
            <if test="type != null">
                AND c.type = #{type}
            </if>
            <if test="state != null">
                AND c.state = #{state}
            </if>
            <if test="info != null and info != ''">
                AND c.info = #{info}
            </if>
            <if test="startDate != null and startDate != ''">
                AND FROM_UNIXTIME(c.create_time, '%Y-%m-%d') &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND FROM_UNIXTIME(c.create_time, '%Y-%m-%d') &lt;= #{endDate}
            </if>
            <if test="cdkCodes != null and cdkCodes != ''">
                AND c.cdk IN
                <foreach collection="cdkCodes.split(',')" item="cdkCode" open="(" separator="," close=")">
                    #{cdkCode}
                </foreach>
            </if>
        </where>
        ORDER BY c.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 查询CDK总数 -->
    <select id="selectCdkCount" resultType="int">
    SELECT 
        COUNT(*)
    FROM 
        vim_cdk c
    <where>
        <!-- 添加一个默认为真但对业务有意义的条件 -->
        c.id IS NOT NULL
        <if test="cdk != null and cdk != ''">
            AND c.cdk LIKE CONCAT('%', #{cdk}, '%')
        </if>
        <if test="type != null">
            AND c.type = #{type}
        </if>
        <if test="state != null">
            AND c.state = #{state}
        </if>
        <if test="info != null and info != ''">
            AND c.info = #{info}
        </if>
        <if test="startDate != null and startDate != ''">
            AND FROM_UNIXTIME(c.create_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND FROM_UNIXTIME(c.create_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test="cdkCodes != null and cdkCodes != ''">
            AND c.cdk IN
            <foreach collection="cdkCodes.split(',')" item="cdkCode" open="(" separator="," close=")">
                #{cdkCode}
            </foreach>
        </if>
    </where>
</select>
    
    <!-- 根据CDK码查询 -->
    <select id="selectCdkByCdkCode" resultMap="VimCdkMap">
        SELECT
            id, cdk, type, value, state, create_time, use_time, use_user, foruser, info
        FROM
            vim_cdk
        <where>
            cdk = #{cdk}
        </where>
        LIMIT 1
    </select>

    <!-- 根据ID查询CDK -->
    <select id="selectCdkById" resultMap="VimCdkMap">
        SELECT
            id, cdk, type, value, state, create_time, use_time, use_user, foruser, info
        FROM
            vim_cdk
        WHERE
            id = #{id}
    </select>
    
    <!-- 插入CDK记录 -->
    <insert id="insertCdk" parameterType="com.ruoyi.project.vimCdkSys.domain.VimCdk">
        INSERT INTO vim_cdk (
            id, cdk, type, value, state, create_time, use_time, use_user, foruser, info
        ) VALUES (
            #{id}, #{cdk}, #{type}, #{value}, #{state}, #{createTime}, #{useTime}, #{useUser}, #{foruser}, #{info}
        )
    </insert>

    <!-- 批量插入CDK -->
    <insert id="batchInsertCdk" parameterType="java.util.List">
        INSERT INTO vim_cdk (
            id, cdk, type, value, state, create_time, use_time, use_user, foruser, info
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.cdk}, #{item.type}, #{item.value}, #{item.state}, #{item.createTime}, #{item.useTime}, #{item.useUser}, #{item.foruser}, #{item.info}
            )
        </foreach>
    </insert>
    
    <!-- 更新CDK状态 -->
    <update id="updateCdkState">
        UPDATE vim_cdk
        SET state = #{state}, use_time = #{useTime}
        WHERE id = #{id}
    </update>
    
    <!-- 删除CDK -->
    <delete id="deleteCdkById">
        DELETE FROM vim_cdk WHERE id = #{id}
    </delete>
    
    <!-- 批量删除CDK -->
    <delete id="batchDeleteCdk">
        DELETE FROM vim_cdk WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- CDK兑换详情结果映射 -->
    <resultMap id="CdkExchangeDetailVOMap" type="com.ruoyi.project.vimCdkSys.domain.vo.CdkExchangeDetailVO">
        <result column="user_id" property="userId" />
        <result column="nickname" property="nickname" />
        <result column="phone" property="phone" />
        <result column="exchange_time" property="exchangeTime" />
        <result column="source" property="source" />
        <result column="source_name" property="sourceName" />
        <result column="cdk" property="cdk" />
        <result column="cdk_type" property="cdkType" />
        <result column="cdk_type_name" property="cdkTypeName" />
        <result column="cdk_value" property="cdkValue" />
    </resultMap>

    <!-- 查询CDK兑换详情列表 -->
    <select id="selectCdkExchangeDetailList" resultMap="CdkExchangeDetailVOMap">
        (
            SELECT
                vu.id AS user_id,
                vu.nickname,
                vu.phone,
                FROM_UNIXTIME(vc.use_time, '%Y-%m-%d %H:%i:%s') AS exchange_time,
                1 AS source,
                '一次性兑换' AS source_name,
                vc.cdk,
                vc.type AS cdk_type,
                CASE
                    WHEN vc.type = 1 THEN '电能'
                    WHEN vc.type = 2 THEN '钥匙'
                    WHEN vc.type = 3 THEN '物品'
                    ELSE '未知'
                END AS cdk_type_name,
                vc.value AS cdk_value
            FROM vim_cdk vc
            INNER JOIN vim_user vu ON vc.use_user = vu.id
            WHERE vc.cdk = #{cdk}
                AND vc.state = 1
                AND vc.use_user IS NOT NULL
        )
        UNION ALL
        (
            SELECT
                vu.id AS user_id,
                vu.nickname,
                vu.phone,
                DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS exchange_time,
                2 AS source,
                '永久可兑换' AS source_name,
                vcl.cdk,
                vc.type AS cdk_type,
                CASE
                    WHEN vc.type = 1 THEN '电能'
                    WHEN vc.type = 2 THEN '钥匙'
                    WHEN vc.type = 3 THEN '物品'
                    ELSE '未知'
                END AS cdk_type_name,
                vc.value AS cdk_value
            FROM vim_cdk_log vcl
            INNER JOIN vim_user vu ON vcl.uid = vu.id
            INNER JOIN vim_cdk vc ON vcl.cdk = vc.cdk
            WHERE vcl.cdk = #{cdk}
        )
        ORDER BY exchange_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询CDK兑换详情总数 -->
    <select id="selectCdkExchangeDetailCount" resultType="int">
        SELECT (
            (
                SELECT COUNT(*)
                FROM vim_cdk vc
                INNER JOIN vim_user vu ON vc.use_user = vu.id
                WHERE vc.cdk = #{cdk}
                    AND vc.state = 1
                    AND vc.use_user IS NOT NULL
            ) + (
                SELECT COUNT(*)
                FROM vim_cdk_log vcl
                INNER JOIN vim_user vu ON vcl.uid = vu.id
                WHERE vcl.cdk = #{cdk}
            )
        ) AS total_count
    </select>

    <!-- 用户选择结果映射 -->
    <resultMap id="UserSelectVOMap" type="com.ruoyi.project.vimCdkSys.domain.vo.UserSelectVO">
        <id column="id" property="id" />
        <result column="nickname" property="nickname" />
        <result column="phone" property="phone" />
        <result column="identity" property="identity" />
        <result column="identity_name" property="identityName" />
    </resultMap>

    <!-- 搜索用户列表 -->
    <select id="searchUsers" resultMap="UserSelectVOMap">
        SELECT
            u.id,
            u.nickname,
            u.phone,
            u.identity,
            CASE
                WHEN u.identity = 1 THEN '普通用户'
                WHEN u.identity = 2 THEN '线上主播'
                WHEN u.identity = 3 THEN '线下主播'
                ELSE '未知'
            END AS identity_name
        FROM vim_user u
        <!-- 数据权限控制：通过vim_user.phone与sys_user.phonenumber关联 -->
        LEFT JOIN sys_user su ON u.phone = su.phonenumber AND su.del_flag = '0'
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        <where>
            u.state = 1
            <if test="searchDTO.keyword != null and searchDTO.keyword != ''">
                AND (u.nickname LIKE CONCAT('%', #{searchDTO.keyword}, '%')
                     OR u.phone LIKE CONCAT('%', #{searchDTO.keyword}, '%'))
            </if>
            <!-- 数据权限过滤 -->
            ${searchDTO.params.dataScope}
        </where>
        ORDER BY u.id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 搜索用户总数 -->
    <select id="searchUsersCount" resultType="int">
        SELECT COUNT(*)
        FROM vim_user u
        <!-- 数据权限控制：通过vim_user.phone与sys_user.phonenumber关联 -->
        LEFT JOIN sys_user su ON u.phone = su.phonenumber AND su.del_flag = '0'
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        <where>
            u.state = 1
            <if test="searchDTO.keyword != null and searchDTO.keyword != ''">
                AND (u.nickname LIKE CONCAT('%', #{searchDTO.keyword}, '%')
                     OR u.phone LIKE CONCAT('%', #{searchDTO.keyword}, '%'))
            </if>
            <!-- 数据权限过滤 -->
            ${searchDTO.params.dataScope}
        </where>
    </select>

    <!-- 用户CDK兑换记录结果映射 -->
    <resultMap id="UserCdkExchangeVOMap" type="com.ruoyi.project.vimCdkSys.domain.vo.UserCdkExchangeVO">
        <result column="cdk_code" property="cdkCode" />
        <result column="exchange_time" property="exchangeTime" />
        <result column="cdk_type" property="cdkType" />
        <result column="cdk_type_name" property="cdkTypeName" />
        <result column="cdk_value" property="cdkValue" />
        <result column="exchange_status" property="exchangeStatus" />
        <result column="exchange_source" property="exchangeSource" />
        <result column="exchange_source_name" property="exchangeSourceName" />
        <result column="user_id" property="userId" />
        <result column="user_nickname" property="userNickname" />
        <result column="user_phone" property="userPhone" />
    </resultMap>

    <!-- 查询用户CDK兑换记录列表 -->
    <select id="selectUserCdkExchangeRecords" resultMap="UserCdkExchangeVOMap">
        (
            SELECT
                vc.cdk AS cdk_code,
                FROM_UNIXTIME(vc.use_time, '%Y-%m-%d %H:%i:%s') AS exchange_time,
                vc.type AS cdk_type,
                CASE
                    WHEN vc.type = 1 THEN '电能'
                    WHEN vc.type = 2 THEN '钥匙'
                    WHEN vc.type = 3 THEN '物品'
                    ELSE '未知'
                END AS cdk_type_name,
                vc.value AS cdk_value,
                '已兑换' AS exchange_status,
                1 AS exchange_source,
                '正常兑换' AS exchange_source_name,
                vu.id AS user_id,
                vu.nickname AS user_nickname,
                vu.phone AS user_phone
            FROM vim_cdk vc
            INNER JOIN vim_user vu ON vc.use_user = vu.id
            WHERE vc.use_user = #{userId}
                AND vc.state = 1
                AND vc.use_user IS NOT NULL
                <if test="startDate != null and startDate != ''">
                    AND FROM_UNIXTIME(vc.use_time, '%Y-%m-%d') &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate != ''">
                    AND FROM_UNIXTIME(vc.use_time, '%Y-%m-%d') &lt;= #{endDate}
                </if>
                <if test="cdkType != null">
                    AND vc.type = #{cdkType}
                </if>
        )
        UNION ALL
        (
            SELECT
                vcl.cdk AS cdk_code,
                DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS exchange_time,
                vc.type AS cdk_type,
                CASE
                    WHEN vc.type = 1 THEN '电能'
                    WHEN vc.type = 2 THEN '钥匙'
                    WHEN vc.type = 3 THEN '物品'
                    ELSE '未知'
                END AS cdk_type_name,
                vc.value AS cdk_value,
                '已兑换' AS exchange_status,
                2 AS exchange_source,
                '永久可兑换' AS exchange_source_name,
                vu.id AS user_id,
                vu.nickname AS user_nickname,
                vu.phone AS user_phone
            FROM vim_cdk_log vcl
            INNER JOIN vim_user vu ON vcl.uid = vu.id
            INNER JOIN vim_cdk vc ON vcl.cdk = vc.cdk
            WHERE vcl.uid = #{userId}
                <if test="cdkType != null">
                    AND vc.type = #{cdkType}
                </if>
        )
        ORDER BY exchange_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询用户CDK兑换记录总数 -->
    <select id="selectUserCdkExchangeRecordsCount" resultType="int">
        SELECT (
            (
                SELECT COUNT(*)
                FROM vim_cdk vc
                WHERE vc.use_user = #{userId}
                    AND vc.state = 1
                    AND vc.use_user IS NOT NULL
                    <if test="startDate != null and startDate != ''">
                        AND FROM_UNIXTIME(vc.use_time, '%Y-%m-%d') &gt;= #{startDate}
                    </if>
                    <if test="endDate != null and endDate != ''">
                        AND FROM_UNIXTIME(vc.use_time, '%Y-%m-%d') &lt;= #{endDate}
                    </if>
                    <if test="cdkType != null">
                        AND vc.type = #{cdkType}
                    </if>
            ) + (
                SELECT COUNT(*)
                FROM vim_cdk_log vcl
                INNER JOIN vim_cdk vc ON vcl.cdk = vc.cdk
                WHERE vcl.uid = #{userId}
                    <if test="cdkType != null">
                        AND vc.type = #{cdkType}
                    </if>
            )
        ) AS total_count
    </select>

    <!-- 用户详细信息结果映射 -->
    <resultMap id="UserDetailInfoVOMap" type="com.ruoyi.project.vimCdkSys.domain.vo.UserDetailInfoVO">
        <id column="id" property="id" />
        <result column="nickname" property="nickname" />
        <result column="username" property="username" />
        <result column="phone" property="phone" />
        <result column="userimage" property="userimage" />
        <result column="balance" property="balance" />
        <result column="key_amount" property="keyAmount" />
        <result column="backpack_value" property="backpackValue" />
        <result column="level" property="level" />
        <result column="exp" property="exp" />
        <result column="identity" property="identity" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="invite_user" property="inviteUser" />
        <result column="superior_nickname" property="superiorNickname" />
        <result column="superior_phone" property="superiorPhone" />
        <result column="superior_identity" property="superiorIdentity" />
    </resultMap>

    <!-- 查询用户详细信息 -->
    <select id="selectUserDetailInfo" resultMap="UserDetailInfoVOMap">
        SELECT
            u.id,
            u.nickname,
            u.username,
            u.phone,
            u.userimage,
            u.coin as balance,
            u.`key` as key_amount,
            COALESCE((
                SELECT SUM(i.price_show * item_counts.count)
                FROM (
                    SELECT
                        ob.itemid,
                        COUNT(*) as count
                    FROM vim_order_box ob
                    WHERE ob.uid = u.id
                    AND ob.state = 1
                    GROUP BY ob.itemid
                ) item_counts
                JOIN vim_item i ON item_counts.itemid = i.id
                WHERE i.delete_time IS NULL
            ), 0) as backpack_value,
            u.level,
            u.exp,
            u.identity,
            u.state,
            u.invite_user,
            -- 上级用户信息
            superior.nickname as superior_nickname,
            superior.phone as superior_phone,
            superior.identity as superior_identity,
            FROM_UNIXTIME(u.create_time, '%Y-%m-%d %H:%i:%s') as create_time,
            CASE
                WHEN u.last_login_time IS NULL OR u.last_login_time = 0 THEN ''
                ELSE FROM_UNIXTIME(u.last_login_time, '%Y-%m-%d %H:%i:%s')
            END as last_login_time
        FROM vim_user u
        LEFT JOIN vim_user superior ON u.invite_user = superior.id AND superior.state = 1
        WHERE u.id = #{userId}
        AND u.state = 1
    </select>
</mapper>